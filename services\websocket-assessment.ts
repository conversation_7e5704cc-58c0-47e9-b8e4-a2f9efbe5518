/**
 * WebSocket Assessment Service
 * Provides real-time assessment notifications to replace polling mechanism
 */

import { io, Socket } from 'socket.io-client';

// WebSocket Event Types
export interface AssessmentWebSocketEvent {
  type: 'assessment-queued' | 'assessment-processing' | 'assessment-completed' | 'assessment-failed';
  jobId: string;
  data: {
    status: string;
    progress: number;
    message?: string;
    resultId?: string; // untuk completed event
    error?: string; // untuk failed event
    queuePosition?: number;
    estimatedTime?: number;
    estimatedTimeRemaining?: number;
  };
}

// Callback types
export type AssessmentEventCallback = (event: AssessmentWebSocketEvent) => void;
export type ConnectionCallback = () => void;
export type ErrorCallback = (error: Error) => void;

// WebSocket Configuration
const WEBSOCKET_CONFIG = {
  // Use API Gateway WebSocket endpoint
  DEVELOPMENT_URL: 'ws://localhost:3001', // Assuming WebSocket server runs on 3001
  PRODUCTION_URL: 'wss://api.chhrone.web.id',
  RECONNECTION_ATTEMPTS: 3, // Reduced for faster fallback
  RECONNECTION_DELAY: 1000,
  TIMEOUT: 10000, // Reduced to 10 seconds for faster fallback
  CONNECTION_TIMEOUT: 5000, // 5 seconds for initial connection
} as const;

export class WebSocketAssessmentService {
  private socket: Socket | null = null;
  private token: string | null = null;
  private isConnected = false;
  private isAuthenticated = false;
  private subscribedJobIds = new Set<string>();
  
  // Event callbacks
  private callbacks: {
    onAssessmentEvent: AssessmentEventCallback | null;
    onConnected: ConnectionCallback | null;
    onDisconnected: ConnectionCallback | null;
    onError: ErrorCallback | null;
    onAuthenticated: ConnectionCallback | null;
  } = {
    onAssessmentEvent: null,
    onConnected: null,
    onDisconnected: null,
    onError: null,
    onAuthenticated: null,
  };

  /**
   * Connect to WebSocket server
   */
  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.token = token;
        
        // Disconnect existing connection
        if (this.socket) {
          this.disconnect();
        }

        // Determine WebSocket URL based on environment
        const wsUrl = process.env.NODE_ENV === 'production' 
          ? WEBSOCKET_CONFIG.PRODUCTION_URL 
          : WEBSOCKET_CONFIG.DEVELOPMENT_URL;

        console.log('WebSocket Assessment: Connecting to', wsUrl);

        // Create socket connection
        this.socket = io(wsUrl, {
          autoConnect: false,
          reconnection: true,
          reconnectionAttempts: WEBSOCKET_CONFIG.RECONNECTION_ATTEMPTS,
          reconnectionDelay: WEBSOCKET_CONFIG.RECONNECTION_DELAY,
          timeout: WEBSOCKET_CONFIG.CONNECTION_TIMEOUT,
          transports: ['websocket', 'polling'],
          forceNew: true,
        });

        this.setupEventListeners();
        
        // Set up connection timeout
        const connectionTimeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, WEBSOCKET_CONFIG.CONNECTION_TIMEOUT);

        // Handle successful connection
        this.socket.once('connect', () => {
          clearTimeout(connectionTimeout);
          this.isConnected = true;
          console.log('WebSocket Assessment: Connected successfully');
          this.authenticate().then(() => {
            resolve();
          }).catch(reject);
        });

        // Handle connection error
        this.socket.once('connect_error', (error) => {
          clearTimeout(connectionTimeout);
          console.error('WebSocket Assessment: Connection failed', error);
          reject(new Error(`WebSocket connection failed: ${error.message}`));
        });

        // Start connection
        this.socket.connect();

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      console.log('WebSocket Assessment: Disconnecting...');
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isAuthenticated = false;
    this.subscribedJobIds.clear();
    this.token = null;
  }

  /**
   * Subscribe to assessment job updates
   */
  subscribeToJob(jobId: string): void {
    if (!this.isAuthenticated || !this.socket) {
      console.warn('WebSocket Assessment: Cannot subscribe - not authenticated');
      return;
    }

    if (this.subscribedJobIds.has(jobId)) {
      console.log('WebSocket Assessment: Already subscribed to job', jobId);
      return;
    }

    console.log('WebSocket Assessment: Subscribing to job', jobId);
    this.socket.emit('subscribe-assessment', { jobId });
    this.subscribedJobIds.add(jobId);
  }

  /**
   * Unsubscribe from assessment job updates
   */
  unsubscribeFromJob(jobId: string): void {
    if (!this.socket) return;

    console.log('WebSocket Assessment: Unsubscribing from job', jobId);
    this.socket.emit('unsubscribe-assessment', { jobId });
    this.subscribedJobIds.delete(jobId);
  }

  /**
   * Set event callbacks
   */
  setCallbacks(callbacks: Partial<typeof this.callbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isAuthenticated: this.isAuthenticated,
      subscribedJobs: Array.from(this.subscribedJobIds),
    };
  }

  /**
   * Authenticate with the server
   */
  private authenticate(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.token) {
        reject(new Error('No socket or token available'));
        return;
      }

      console.log('WebSocket Assessment: Authenticating...');

      // Set up authentication timeout
      const authTimeout = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, 10000);

      // Handle authentication success
      this.socket.once('authenticated', () => {
        clearTimeout(authTimeout);
        this.isAuthenticated = true;
        console.log('WebSocket Assessment: Authenticated successfully');
        this.callbacks.onAuthenticated?.();
        resolve();
      });

      // Handle authentication failure
      this.socket.once('auth-error', (error) => {
        clearTimeout(authTimeout);
        console.error('WebSocket Assessment: Authentication failed', error);
        reject(new Error(`Authentication failed: ${error.message || 'Unknown error'}`));
      });

      // Send authentication
      this.socket.emit('authenticate', { token: this.token });
    });
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      this.isConnected = true;
      console.log('WebSocket Assessment: Connected');
      this.callbacks.onConnected?.();
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      this.isAuthenticated = false;
      console.log('WebSocket Assessment: Disconnected', reason);
      this.callbacks.onDisconnected?.();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket Assessment: Connection error', error);
      this.callbacks.onError?.(new Error(`Connection error: ${error.message}`));
    });

    // Assessment events
    this.socket.on('assessment-update', (event: AssessmentWebSocketEvent) => {
      console.log('WebSocket Assessment: Received update', event);
      this.callbacks.onAssessmentEvent?.(event);
    });

    // Error events
    this.socket.on('error', (error) => {
      console.error('WebSocket Assessment: Server error', error);
      this.callbacks.onError?.(new Error(`Server error: ${error.message || 'Unknown error'}`));
    });
  }
}

// Singleton instance
let assessmentWebSocketInstance: WebSocketAssessmentService | null = null;

/**
 * Get singleton WebSocket Assessment Service instance
 */
export function getAssessmentWebSocketService(): WebSocketAssessmentService {
  if (!assessmentWebSocketInstance) {
    assessmentWebSocketInstance = new WebSocketAssessmentService();
  }
  return assessmentWebSocketInstance;
}

/**
 * Utility function to check if WebSocket is supported
 */
export function isWebSocketSupported(): boolean {
  return typeof WebSocket !== 'undefined' || typeof window !== 'undefined';
}
